import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

enum ButtonType { primary, secondary, outline, text }

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final Widget? icon;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final double borderRadius;
  final TextStyle? textStyle;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.width,
    this.height,
    this.padding,
    this.icon,
    this.isLoading = false,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.borderRadius = 8.0,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final bool isDisabled = onPressed == null || isLoading;
    
    return SizedBox(
      width: width,
      height: height ?? 48,
      child: _buildButton(context, isDisabled),
    );
  }

  Widget _buildButton(BuildContext context, bool isDisabled) {
    switch (type) {
      case ButtonType.primary:
        return _buildPrimaryButton(context, isDisabled);
      case ButtonType.secondary:
        return _buildSecondaryButton(context, isDisabled);
      case ButtonType.outline:
        return _buildOutlineButton(context, isDisabled);
      case ButtonType.text:
        return _buildTextButton(context, isDisabled);
    }
  }

  Widget _buildPrimaryButton(BuildContext context, bool isDisabled) {
    return ElevatedButton(
      onPressed: isDisabled ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? AppColors.primary,
        foregroundColor: textColor ?? AppColors.white,
        disabledBackgroundColor: AppColors.greyLight,
        disabledForegroundColor: AppColors.textHint,
        elevation: isDisabled ? 0 : 2,
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildSecondaryButton(BuildContext context, bool isDisabled) {
    return ElevatedButton(
      onPressed: isDisabled ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? AppColors.secondary,
        foregroundColor: textColor ?? AppColors.white,
        disabledBackgroundColor: AppColors.greyLight,
        disabledForegroundColor: AppColors.textHint,
        elevation: isDisabled ? 0 : 2,
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildOutlineButton(BuildContext context, bool isDisabled) {
    return OutlinedButton(
      onPressed: isDisabled ? null : onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: textColor ?? AppColors.primary,
        disabledForegroundColor: AppColors.textHint,
        side: BorderSide(
          color: isDisabled 
              ? AppColors.borderLight 
              : (borderColor ?? AppColors.primary),
          width: 1.5,
        ),
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildTextButton(BuildContext context, bool isDisabled) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isDisabled ? null : onPressed,
        borderRadius: BorderRadius.circular(borderRadius),
        child: Container(
          padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: _buildButtonContent(),
        ),
      ),
    );
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return const SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          const SizedBox(width: 8),
          Text(
            text,
            style: textStyle ?? _getDefaultTextStyle(),
          ),
        ],
      );
    }

    return Text(
      text,
      style: textStyle ?? _getDefaultTextStyle(),
    );
  }

  TextStyle _getDefaultTextStyle() {
    switch (type) {
      case ButtonType.primary:
      case ButtonType.secondary:
        return AppTextStyles.buttonMedium;
      case ButtonType.outline:
        return AppTextStyles.buttonMedium.copyWith(
          color: textColor ?? AppColors.primary,
        );
      case ButtonType.text:
        return AppTextStyles.buttonMedium.copyWith(
          color: textColor ?? AppColors.primary,
        );
    }
  }
}

// Convenience constructors
class PrimaryButton extends CustomButton {
  const PrimaryButton({
    super.key,
    required super.text,
    super.onPressed,
    super.width,
    super.height,
    super.padding,
    super.icon,
    super.isLoading,
    super.backgroundColor,
    super.textColor,
    super.borderRadius,
    super.textStyle,
  }) : super(type: ButtonType.primary);
}

class SecondaryButton extends CustomButton {
  const SecondaryButton({
    super.key,
    required super.text,
    super.onPressed,
    super.width,
    super.height,
    super.padding,
    super.icon,
    super.isLoading,
    super.backgroundColor,
    super.textColor,
    super.borderRadius,
    super.textStyle,
  }) : super(type: ButtonType.secondary);
}

class CustomOutlineButton extends CustomButton {
  const CustomOutlineButton({
    super.key,
    required super.text,
    super.onPressed,
    super.width,
    super.height,
    super.padding,
    super.icon,
    super.isLoading,
    super.textColor,
    super.borderColor,
    super.borderRadius,
    super.textStyle,
  }) : super(type: ButtonType.outline);
}

class CustomTextButton extends CustomButton {
  const CustomTextButton({
    super.key,
    required super.text,
    super.onPressed,
    super.width,
    super.height,
    super.padding,
    super.icon,
    super.isLoading,
    super.textColor,
    super.borderRadius,
    super.textStyle,
  }) : super(type: ButtonType.text);
}
