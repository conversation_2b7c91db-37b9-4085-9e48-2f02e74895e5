import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../shared/widgets/checkout_step_indicator.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/models/order.dart';
import '../../../shared/models/product.dart';
import '../../cart/models/cart_item.dart';
import 'widgets/shipping_step.dart';
import 'widgets/payment_step.dart';
import 'widgets/review_step.dart';

class CheckoutScreen extends StatefulWidget {
  final List<CartItem>? cartItems;

  const CheckoutScreen({
    super.key,
    this.cartItems,
  });

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {
  late CheckoutState _checkoutState;
  final PageController _pageController = PageController();

  final List<String> _stepTitles = [
    'Shipping',
    'Payment',
    'Review',
  ];

  @override
  void initState() {
    super.initState();
    _initializeCheckoutState();
  }

  void _initializeCheckoutState() {
    // Get cart items (in a real app, this would come from a cart provider/bloc)
    final items = widget.cartItems ?? _getMockCartItems();
    final orderSummary = OrderSummary.fromCartItems(
      items,
      shippingCost: 9.99,
      taxRate: 0.08,
    );

    _checkoutState = CheckoutState(
      currentStep: CheckoutStep.shipping,
      orderSummary: orderSummary,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(_checkoutState.stepTitle),
        elevation: 0,
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.textPrimary,
      ),
      body: Column(
        children: [
          // Step Indicator
          Container(
            color: AppColors.white,
            child: AnimatedCheckoutStepIndicator(
              currentStep: _checkoutState.stepIndex,
              steps: _stepTitles,
            ),
          ),
          
          // Content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                ShippingStep(
                  checkoutState: _checkoutState,
                  onAddressSelected: _onAddressSelected,
                  onContinue: _goToPaymentStep,
                ),
                PaymentStep(
                  checkoutState: _checkoutState,
                  onPaymentMethodSelected: _onPaymentMethodSelected,
                  onContinue: _goToReviewStep,
                  onBack: _goToShippingStep,
                ),
                ReviewStep(
                  checkoutState: _checkoutState,
                  onPlaceOrder: _placeOrder,
                  onBack: _goBackToPaymentStep,
                  onEditShipping: _goToShippingStep,
                  onEditPayment: _goBackToPaymentStep,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onAddressSelected(ShippingAddress address) {
    setState(() {
      _checkoutState = _checkoutState.copyWith(
        shippingAddress: address,
      );
    });
  }

  void _onPaymentMethodSelected(PaymentMethod paymentMethod) {
    setState(() {
      _checkoutState = _checkoutState.copyWith(
        paymentMethod: paymentMethod,
      );
    });
  }

  void _goToPaymentStep() {
    if (_checkoutState.canProceedToPayment) {
      setState(() {
        _checkoutState = _checkoutState.copyWith(
          currentStep: CheckoutStep.payment,
        );
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToReviewStep() {
    if (_checkoutState.canProceedToReview) {
      setState(() {
        _checkoutState = _checkoutState.copyWith(
          currentStep: CheckoutStep.review,
        );
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToShippingStep() {
    setState(() {
      _checkoutState = _checkoutState.copyWith(
        currentStep: CheckoutStep.shipping,
      );
    });
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _goBackToPaymentStep() {
    setState(() {
      _checkoutState = _checkoutState.copyWith(
        currentStep: CheckoutStep.payment,
      );
    });
    _pageController.animateToPage(
      1,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _placeOrder() async {
    if (!_checkoutState.canPlaceOrder) return;

    setState(() {
      _checkoutState = _checkoutState.copyWith(isLoading: true);
    });

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Navigate to success screen
      if (mounted) {
        context.pushReplacement('/checkout-success');
      }
    } catch (e) {
      setState(() {
        _checkoutState = _checkoutState.copyWith(
          isLoading: false,
          error: 'Failed to place order. Please try again.',
        );
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_checkoutState.error!),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // Mock data for demonstration
  List<CartItem> _getMockCartItems() {
    return [
      CartItem(
        id: '1',
        product: Product(
          id: '1',
          name: 'Wireless Bluetooth Headphones',
          description: 'High-quality wireless headphones with noise cancellation',
          price: 99.99,
          originalPrice: 129.99,
          images: ['https://via.placeholder.com/300x300?text=Headphones'],
          category: 'Electronics',
          rating: 4.5,
          reviewCount: 128,
          isInStock: true,
          colors: ['Black', 'White'],
          sizes: [],
          brand: 'TechBrand',
          isFeatured: true,
          isOnSale: true,
          createdAt: DateTime.now(),
        ),
        quantity: 1,
        selectedColor: 'Black',
      ),
      CartItem(
        id: '2',
        product: Product(
          id: '2',
          name: 'Cotton T-Shirt',
          description: 'Comfortable cotton t-shirt for everyday wear',
          price: 24.99,
          images: ['https://via.placeholder.com/300x300?text=T-Shirt'],
          category: 'Fashion',
          rating: 4.2,
          reviewCount: 89,
          isInStock: true,
          colors: ['Blue', 'Red', 'Green'],
          sizes: ['S', 'M', 'L', 'XL'],
          brand: 'FashionBrand',
          createdAt: DateTime.now(),
        ),
        quantity: 2,
        selectedSize: 'M',
        selectedColor: 'Blue',
      ),
    ];
  }
}
