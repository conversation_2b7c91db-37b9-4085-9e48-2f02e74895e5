import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? label;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final bool enabled;
  final bool readOnly;
  final bool autofocus;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final void Function(String)? onSubmitted;
  final List<TextInputFormatter>? inputFormatters;
  final EdgeInsetsGeometry? contentPadding;
  final Color? fillColor;
  final Color? borderColor;
  final double borderRadius;
  final TextStyle? textStyle;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;

  const CustomTextField({
    super.key,
    this.controller,
    this.label,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.validator,
    this.onChanged,
    this.onTap,
    this.onSubmitted,
    this.inputFormatters,
    this.contentPadding,
    this.fillColor,
    this.borderColor,
    this.borderRadius = 8.0,
    this.textStyle,
    this.labelStyle,
    this.hintStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: labelStyle ?? AppTextStyles.labelMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          keyboardType: keyboardType,
          textInputAction: textInputAction,
          maxLines: maxLines,
          minLines: minLines,
          maxLength: maxLength,
          enabled: enabled,
          readOnly: readOnly,
          autofocus: autofocus,
          validator: validator,
          onChanged: onChanged,
          onTap: onTap,
          onFieldSubmitted: onSubmitted,
          inputFormatters: inputFormatters,
          style: textStyle ?? AppTextStyles.bodyMedium,
          decoration: InputDecoration(
            hintText: hintText,
            helperText: helperText,
            errorText: errorText,
            prefixIcon: prefixIcon != null 
                ? Icon(
                    prefixIcon,
                    color: AppColors.textSecondary,
                    size: 20,
                  )
                : null,
            suffixIcon: suffixIcon,
            filled: true,
            fillColor: fillColor ?? (enabled ? AppColors.white : AppColors.greyLight),
            contentPadding: contentPadding ?? const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            hintStyle: hintStyle ?? AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textHint,
            ),
            helperStyle: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            errorStyle: AppTextStyles.bodySmall.copyWith(
              color: AppColors.error,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(
                color: borderColor ?? AppColors.border,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: BorderSide(
                color: borderColor ?? AppColors.border,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: const BorderSide(
                color: AppColors.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: const BorderSide(
                color: AppColors.error,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: const BorderSide(
                color: AppColors.error,
                width: 2,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              borderSide: const BorderSide(
                color: AppColors.borderLight,
                width: 1,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Specialized text field widgets
class EmailTextField extends CustomTextField {
  const EmailTextField({
    super.key,
    super.controller,
    super.label = 'Email',
    super.hintText = 'Enter your email',
    super.validator,
    super.onChanged,
    super.onSubmitted,
  }) : super(
          keyboardType: TextInputType.emailAddress,
          prefixIcon: Icons.email_outlined,
          textInputAction: TextInputAction.next,
        );
}

class PasswordTextField extends CustomTextField {
  final bool isPasswordVisible;
  final VoidCallback? onToggleVisibility;

  const PasswordTextField({
    super.key,
    super.controller,
    super.label = 'Password',
    super.hintText = 'Enter your password',
    super.validator,
    super.onChanged,
    super.onSubmitted,
    this.isPasswordVisible = false,
    this.onToggleVisibility,
  }) : super(
          obscureText: !isPasswordVisible,
          keyboardType: TextInputType.visiblePassword,
          prefixIcon: Icons.lock_outlined,
          textInputAction: TextInputAction.done,
        );

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      controller: controller,
      label: label,
      hintText: hintText,
      validator: validator,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      obscureText: !isPasswordVisible,
      keyboardType: keyboardType,
      prefixIcon: prefixIcon,
      textInputAction: textInputAction,
      suffixIcon: IconButton(
        icon: Icon(
          isPasswordVisible 
              ? Icons.visibility_outlined 
              : Icons.visibility_off_outlined,
          color: AppColors.textSecondary,
        ),
        onPressed: onToggleVisibility,
      ),
    );
  }
}

class SearchTextField extends CustomTextField {
  const SearchTextField({
    super.key,
    super.controller,
    super.hintText = 'Search...',
    super.onChanged,
    super.onSubmitted,
    VoidCallback? onClear,
  }) : super(
          keyboardType: TextInputType.text,
          prefixIcon: Icons.search_outlined,
          textInputAction: TextInputAction.search,
        );

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      controller: controller,
      hintText: hintText,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      keyboardType: keyboardType,
      prefixIcon: prefixIcon,
      textInputAction: textInputAction,
      suffixIcon: controller?.text.isNotEmpty == true
          ? IconButton(
              icon: const Icon(
                Icons.clear,
                color: AppColors.textSecondary,
              ),
              onPressed: () {
                controller?.clear();
                onChanged?.call('');
              },
            )
          : null,
    );
  }
}

class PhoneTextField extends CustomTextField {
  const PhoneTextField({
    super.key,
    super.controller,
    super.label = 'Phone Number',
    super.hintText = 'Enter your phone number',
    super.validator,
    super.onChanged,
    super.onSubmitted,
  }) : super(
          keyboardType: TextInputType.phone,
          prefixIcon: Icons.phone_outlined,
          textInputAction: TextInputAction.next,
        );
}
