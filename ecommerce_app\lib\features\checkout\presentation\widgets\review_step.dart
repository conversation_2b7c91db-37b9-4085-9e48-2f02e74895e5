import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/models/order.dart';
import '../../../../shared/widgets/address_card.dart';
import '../../../../shared/widgets/payment_method_card.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/order_summary_card.dart';
import '../../../../shared/widgets/promo_code_widget.dart';

class ReviewStep extends StatefulWidget {
  final CheckoutState checkoutState;
  final VoidCallback onPlaceOrder;
  final VoidCallback onBack;
  final VoidCallback onEditShipping;
  final VoidCallback onEditPayment;
  final Function(String) onApplyPromoCode;
  final VoidCallback onRemovePromoCode;
  final bool isApplyingPromoCode;

  const ReviewStep({
    super.key,
    required this.checkoutState,
    required this.onPlaceOrder,
    required this.onBack,
    required this.onEditShipping,
    required this.onEditPayment,
    required this.onApplyPromoCode,
    required this.onRemovePromoCode,
    required this.isApplyingPromoCode,
  });

  @override
  State<ReviewStep> createState() => _ReviewStepState();
}

class _ReviewStepState extends State<ReviewStep> {
  bool _isOrderSummaryExpanded = false;
  bool _agreeToTerms = false;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Shipping Address Section
          _buildSectionHeader(
            'Shipping Address',
            Icons.location_on_outlined,
            widget.onEditShipping,
          ),
          const SizedBox(height: 12),
          if (widget.checkoutState.shippingAddress != null)
            CompactAddressCard(
              address: widget.checkoutState.shippingAddress!,
              onEdit: widget.onEditShipping,
            ),
          
          const SizedBox(height: 24),
          
          // Payment Method Section
          _buildSectionHeader(
            'Payment Method',
            Icons.payment_outlined,
            widget.onEditPayment,
          ),
          const SizedBox(height: 12),
          if (widget.checkoutState.paymentMethod != null)
            CompactPaymentMethodCard(
              paymentMethod: widget.checkoutState.paymentMethod!,
              onEdit: widget.onEditPayment,
            ),
          
          const SizedBox(height: 24),
          
          // Order Summary Section
          _buildSectionHeader(
            'Order Summary',
            Icons.receipt_long_outlined,
            null,
          ),
          const SizedBox(height: 12),
          OrderSummaryCard(
            orderSummary: widget.checkoutState.orderSummary,
            showItems: true,
            isExpanded: _isOrderSummaryExpanded,
            onToggleExpanded: () {
              setState(() {
                _isOrderSummaryExpanded = !_isOrderSummaryExpanded;
              });
            },
            promoCodeWidget: PromoCodeWidget(
              appliedPromoCode: widget.checkoutState.orderSummary.promoCode,
              discountAmount: widget.checkoutState.orderSummary.discount > 0
                  ? widget.checkoutState.orderSummary.discount
                  : null,
              onApplyPromoCode: widget.onApplyPromoCode,
              onRemovePromoCode: widget.onRemovePromoCode,
              isLoading: widget.isApplyingPromoCode,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Delivery Information
          _buildDeliveryInfo(),
          
          const SizedBox(height: 24),
          
          // Terms and Conditions
          _buildTermsAndConditions(),
          
          const SizedBox(height: 24),
          
          // Error Message
          if (widget.checkoutState.error != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.error.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: AppColors.error,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.checkoutState.error!,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // Navigation Buttons
          Row(
            children: [
              Expanded(
                child: CustomOutlineButton(
                  text: 'Back',
                  onPressed: widget.onBack,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: PrimaryButton(
                  text: 'Place Order',
                  isLoading: widget.checkoutState.isLoading,
                  onPressed: _canPlaceOrder() ? widget.onPlaceOrder : null,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Security Notice
          _buildSecurityNotice(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, VoidCallback? onEdit) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        if (onEdit != null)
          TextButton(
            onPressed: onEdit,
            child: Text(
              'Edit',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDeliveryInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.info.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.info.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_shipping_outlined,
                color: AppColors.info,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Delivery Information',
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.info,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInfoRow(
            Icons.schedule,
            'Estimated Delivery',
            '3-5 business days',
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            Icons.security,
            'Order Protection',
            'Your order is protected by our guarantee',
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            Icons.support_agent,
            'Customer Support',
            '24/7 support available',
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppColors.textSecondary,
          size: 16,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: AppTextStyles.bodySmall.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildTermsAndConditions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Terms & Conditions',
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Checkbox(
                value: _agreeToTerms,
                onChanged: (value) {
                  setState(() {
                    _agreeToTerms = value ?? false;
                  });
                },
                activeColor: AppColors.primary,
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: RichText(
                    text: TextSpan(
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      children: [
                        const TextSpan(
                          text: 'I agree to the ',
                        ),
                        TextSpan(
                          text: 'Terms of Service',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const TextSpan(
                          text: ' and ',
                        ),
                        TextSpan(
                          text: 'Privacy Policy',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const TextSpan(
                          text: '. I understand that my order will be processed according to these terms.',
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityNotice() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.security,
            color: AppColors.success,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Your payment information is secure and encrypted',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _canPlaceOrder() {
    return widget.checkoutState.canPlaceOrder && 
           _agreeToTerms && 
           !widget.checkoutState.isLoading;
  }
}
