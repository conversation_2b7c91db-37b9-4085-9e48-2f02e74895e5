import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import 'custom_text_field.dart';
import 'custom_button.dart';

class PromoCodeWidget extends StatefulWidget {
  final String? appliedPromoCode;
  final double? discountAmount;
  final Function(String) onApplyPromoCode;
  final VoidCallback? onRemovePromoCode;
  final bool isLoading;

  const PromoCodeWidget({
    super.key,
    this.appliedPromoCode,
    this.discountAmount,
    required this.onApplyPromoCode,
    this.onRemovePromoCode,
    this.isLoading = false,
  });

  @override
  State<PromoCodeWidget> createState() => _PromoCodeWidgetState();
}

class _PromoCodeWidgetState extends State<PromoCodeWidget>
    with TickerProviderStateMixin {
  final TextEditingController _promoController = TextEditingController();
  late AnimationController _successController;
  late Animation<double> _successAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _successController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _successAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _successController,
      curve: Curves.elasticOut,
    ));

    if (widget.appliedPromoCode != null) {
      _promoController.text = widget.appliedPromoCode!;
      _isExpanded = true;
      _successController.forward();
    }
  }

  @override
  void didUpdateWidget(PromoCodeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.appliedPromoCode != oldWidget.appliedPromoCode) {
      if (widget.appliedPromoCode != null) {
        _promoController.text = widget.appliedPromoCode!;
        _successController.forward();
      } else {
        _successController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _promoController.dispose();
    _successController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: widget.appliedPromoCode != null 
              ? AppColors.success 
              : AppColors.border,
        ),
      ),
      child: Column(
        children: [
          // Header
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _toggleExpanded,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              mouseCursor: SystemMouseCursors.click,
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: widget.appliedPromoCode != null
                            ? AppColors.success.withOpacity(0.1)
                            : AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        widget.appliedPromoCode != null
                            ? Icons.check_circle_outline
                            : Icons.local_offer_outlined,
                        color: widget.appliedPromoCode != null
                            ? AppColors.success
                            : AppColors.primary,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.appliedPromoCode != null
                                ? 'Promo Code Applied'
                                : 'Have a Promo Code?',
                            style: AppTextStyles.titleSmall.copyWith(
                              fontWeight: FontWeight.w600,
                              color: widget.appliedPromoCode != null
                                  ? AppColors.success
                                  : AppColors.textPrimary,
                            ),
                          ),
                          if (widget.appliedPromoCode != null && widget.discountAmount != null)
                            Text(
                              'You saved \$${widget.discountAmount!.toStringAsFixed(2)}',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.success,
                                fontWeight: FontWeight.w500,
                              ),
                            )
                          else
                            Text(
                              'Enter your code to get discount',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                        ],
                      ),
                    ),
                    if (widget.appliedPromoCode != null)
                      ScaleTransition(
                        scale: _successAnimation,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.success,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            widget.appliedPromoCode!,
                            style: AppTextStyles.labelSmall.copyWith(
                              color: AppColors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    const SizedBox(width: 8),
                    Icon(
                      _isExpanded 
                          ? Icons.keyboard_arrow_up 
                          : Icons.keyboard_arrow_down,
                      color: AppColors.textSecondary,
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Expandable Content
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isExpanded ? null : 0,
            child: _isExpanded ? _buildPromoCodeForm() : null,
          ),
        ],
      ),
    );
  }

  Widget _buildPromoCodeForm() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        children: [
          const Divider(height: 1),
          const SizedBox(height: 16),
          
          if (widget.appliedPromoCode == null) ...[
            // Promo Code Input
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _promoController,
                    hintText: 'Enter promo code',
                    prefixIcon: Icons.local_offer_outlined,
                    textInputAction: TextInputAction.done,
                    onSubmitted: (_) => _applyPromoCode(),
                  ),
                ),
                const SizedBox(width: 12),
                PrimaryButton(
                  text: 'Apply',
                  isLoading: widget.isLoading,
                  onPressed: _applyPromoCode,
                  width: 80,
                  height: 48,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Popular Promo Codes
            _buildPopularPromoCodes(),
          ] else ...[
            // Applied Promo Code Actions
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.success.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: AppColors.success,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Code "${widget.appliedPromoCode}" applied successfully',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.success,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                CustomOutlineButton(
                  text: 'Remove',
                  onPressed: widget.onRemovePromoCode,
                  width: 80,
                  height: 40,
                  borderColor: AppColors.error,
                  textColor: AppColors.error,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPopularPromoCodes() {
    final popularCodes = [
      {'code': 'SAVE10', 'description': '10% off your order'},
      {'code': 'FREESHIP', 'description': 'Free shipping'},
      {'code': 'WELCOME20', 'description': '20% off for new customers'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Popular Codes',
          style: AppTextStyles.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: popularCodes.map((promo) => _buildPromoChip(
            promo['code']!,
            promo['description']!,
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildPromoChip(String code, String description) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _applyQuickPromo(code),
        borderRadius: BorderRadius.circular(20),
        mouseCursor: SystemMouseCursors.click,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: AppColors.primary.withOpacity(0.3)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                code,
                style: AppTextStyles.labelSmall.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
              Text(
                description,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  void _applyPromoCode() {
    final code = _promoController.text.trim().toUpperCase();
    if (code.isNotEmpty) {
      widget.onApplyPromoCode(code);
    }
  }

  void _applyQuickPromo(String code) {
    _promoController.text = code;
    widget.onApplyPromoCode(code);
  }
}
