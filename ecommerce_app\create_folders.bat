@echo off
mkdir lib\core\theme
mkdir lib\core\utils
mkdir lib\core\services
mkdir lib\core\errors
mkdir lib\shared\widgets
mkdir lib\shared\models
mkdir lib\features\auth\data
mkdir lib\features\auth\domain
mkdir lib\features\auth\presentation
mkdir lib\features\products\data
mkdir lib\features\products\domain
mkdir lib\features\products\presentation
mkdir lib\features\cart\data
mkdir lib\features\cart\domain
mkdir lib\features\cart\presentation
mkdir lib\features\search\data
mkdir lib\features\search\domain
mkdir lib\features\search\presentation
mkdir lib\features\profile\data
mkdir lib\features\profile\domain
mkdir lib\features\profile\presentation
mkdir lib\features\orders\data
mkdir lib\features\orders\domain
mkdir lib\features\orders\presentation
mkdir lib\features\onboarding\presentation
echo Folders created successfully!
