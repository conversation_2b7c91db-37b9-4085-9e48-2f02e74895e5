import 'product.dart';
import '../../features/cart/models/cart_item.dart';

// Order Status Enum
enum OrderStatus {
  pending,
  confirmed,
  processing,
  shipped,
  delivered,
  cancelled,
  refunded
}

// Order Model
class Order {
  final String id;
  final String userId;
  final List<CartItem> items;
  final ShippingAddress shippingAddress;
  final PaymentMethod paymentMethod;
  final double subtotal;
  final double shippingCost;
  final double tax;
  final double discount;
  final double total;
  final OrderStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? trackingNumber;
  final String? notes;

  Order({
    required this.id,
    required this.userId,
    required this.items,
    required this.shippingAddress,
    required this.paymentMethod,
    required this.subtotal,
    required this.shippingCost,
    required this.tax,
    required this.discount,
    required this.total,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.trackingNumber,
    this.notes,
  });

  Order copyWith({
    String? id,
    String? userId,
    List<CartItem>? items,
    ShippingAddress? shippingAddress,
    PaymentMethod? paymentMethod,
    double? subtotal,
    double? shippingCost,
    double? tax,
    double? discount,
    double? total,
    OrderStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? trackingNumber,
    String? notes,
  }) {
    return Order(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      items: items ?? this.items,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      subtotal: subtotal ?? this.subtotal,
      shippingCost: shippingCost ?? this.shippingCost,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      notes: notes ?? this.notes,
    );
  }

  String get statusDisplayName {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.shipped:
        return 'Shipped';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.refunded:
        return 'Refunded';
    }
  }

  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'],
      userId: json['userId'],
      items: (json['items'] as List)
          .map((item) => CartItem.fromJson(item))
          .toList(),
      shippingAddress: ShippingAddress.fromJson(json['shippingAddress']),
      paymentMethod: PaymentMethod.fromJson(json['paymentMethod']),
      subtotal: json['subtotal'].toDouble(),
      shippingCost: json['shippingCost'].toDouble(),
      tax: json['tax'].toDouble(),
      discount: json['discount'].toDouble(),
      total: json['total'].toDouble(),
      status: OrderStatus.values.firstWhere(
        (e) => e.toString() == 'OrderStatus.${json['status']}',
      ),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      trackingNumber: json['trackingNumber'],
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'items': items.map((item) => item.toJson()).toList(),
      'shippingAddress': shippingAddress.toJson(),
      'paymentMethod': paymentMethod.toJson(),
      'subtotal': subtotal,
      'shippingCost': shippingCost,
      'tax': tax,
      'discount': discount,
      'total': total,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'trackingNumber': trackingNumber,
      'notes': notes,
    };
  }
}

// Order Summary Model for Checkout
class OrderSummary {
  final List<CartItem> items;
  final double subtotal;
  final double shippingCost;
  final double tax;
  final double discount;
  final double total;
  final String? promoCode;

  OrderSummary({
    required this.items,
    required this.subtotal,
    required this.shippingCost,
    required this.tax,
    required this.discount,
    required this.total,
    this.promoCode,
  });

  OrderSummary copyWith({
    List<CartItem>? items,
    double? subtotal,
    double? shippingCost,
    double? tax,
    double? discount,
    double? total,
    String? promoCode,
  }) {
    return OrderSummary(
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      shippingCost: shippingCost ?? this.shippingCost,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      promoCode: promoCode ?? this.promoCode,
    );
  }

  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);

  static OrderSummary fromCartItems(List<CartItem> items, {
    double shippingCost = 0.0,
    double taxRate = 0.08, // 8% tax rate
    double discount = 0.0,
    String? promoCode,
  }) {
    final subtotal = items.fold(0.0, (sum, item) => sum + item.totalPrice);
    final tax = subtotal * taxRate;
    final total = subtotal + shippingCost + tax - discount;

    return OrderSummary(
      items: items,
      subtotal: subtotal,
      shippingCost: shippingCost,
      tax: tax,
      discount: discount,
      total: total,
      promoCode: promoCode,
    );
  }

  // Helper method to apply promo code
  OrderSummary applyPromoCode(String promoCode) {
    double newDiscount = 0.0;
    double newShippingCost = shippingCost;

    // Mock promo code logic
    switch (promoCode.toUpperCase()) {
      case 'SAVE10':
        newDiscount = subtotal * 0.10; // 10% off
        break;
      case 'SAVE20':
        newDiscount = subtotal * 0.20; // 20% off
        break;
      case 'WELCOME20':
        newDiscount = subtotal * 0.20; // 20% off for new customers
        break;
      case 'FREESHIP':
        newShippingCost = 0.0; // Free shipping
        break;
      case 'SAVE50':
        newDiscount = 50.0; // $50 off
        break;
      default:
        // Invalid promo code
        return this;
    }

    final newTotal = subtotal + newShippingCost + tax - newDiscount;

    return copyWith(
      discount: newDiscount,
      shippingCost: newShippingCost,
      total: newTotal,
      promoCode: promoCode,
    );
  }

  // Helper method to remove promo code
  OrderSummary removePromoCode() {
    // Recalculate without discount and with original shipping
    final originalShipping = 9.99; // Default shipping cost
    final newTotal = subtotal + originalShipping + tax;

    return copyWith(
      discount: 0.0,
      shippingCost: originalShipping,
      total: newTotal,
      promoCode: null,
    );
  }
}

// Checkout Step Enum
enum CheckoutStep {
  shipping,
  payment,
  review,
  confirmation
}

// Checkout State Model
class CheckoutState {
  final CheckoutStep currentStep;
  final ShippingAddress? shippingAddress;
  final PaymentMethod? paymentMethod;
  final OrderSummary orderSummary;
  final bool isLoading;
  final String? error;

  CheckoutState({
    required this.currentStep,
    this.shippingAddress,
    this.paymentMethod,
    required this.orderSummary,
    this.isLoading = false,
    this.error,
  });

  CheckoutState copyWith({
    CheckoutStep? currentStep,
    ShippingAddress? shippingAddress,
    PaymentMethod? paymentMethod,
    OrderSummary? orderSummary,
    bool? isLoading,
    String? error,
  }) {
    return CheckoutState(
      currentStep: currentStep ?? this.currentStep,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      orderSummary: orderSummary ?? this.orderSummary,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  bool get canProceedToPayment => shippingAddress != null;
  bool get canProceedToReview => shippingAddress != null && paymentMethod != null;
  bool get canPlaceOrder => canProceedToReview && !isLoading;

  String get stepTitle {
    switch (currentStep) {
      case CheckoutStep.shipping:
        return 'Shipping Address';
      case CheckoutStep.payment:
        return 'Payment Method';
      case CheckoutStep.review:
        return 'Review Order';
      case CheckoutStep.confirmation:
        return 'Order Confirmed';
    }
  }

  int get stepIndex {
    switch (currentStep) {
      case CheckoutStep.shipping:
        return 0;
      case CheckoutStep.payment:
        return 1;
      case CheckoutStep.review:
        return 2;
      case CheckoutStep.confirmation:
        return 3;
    }
  }
}
