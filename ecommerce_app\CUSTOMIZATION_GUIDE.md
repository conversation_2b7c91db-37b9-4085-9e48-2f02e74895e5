# 🎨 Customization Guide

Complete guide to customize the Modern Ecommerce Flutter App for your brand and business needs.

## 🏷️ **Brand Customization**

### **1. App Name & Identity**

#### **Change App Name**
Edit `pubspec.yaml`:
```yaml
name: your_store_name
description: Your amazing ecommerce store
```

#### **Update App Display Name**
**Android**: Edit `android/app/src/main/AndroidManifest.xml`:
```xml
<application
    android:label="Your Store Name"
    ...>
```

**iOS**: Edit `ios/Runner/Info.plist`:
```xml
<key>CFBundleDisplayName</key>
<string>Your Store Name</string>
```

### **2. Color Scheme Customization**

#### **Primary Brand Colors**
Edit `lib/core/theme/app_colors.dart`:
```dart
class AppColors {
  // Your brand primary color
  static const Color primary = Color(0xFF1976D2);      // Blue
  static const Color secondary = Color(0xFF388E3C);    // Green
  
  // Background colors (keep unified for clean look)
  static const Color background = Color(0xFFFAFAFA);   // Light Gray
  static const Color surface = Color(0xFFFFFFFF);      // White
  
  // Customize accent colors
  static const Color success = Color(0xFF4CAF50);      // Your success color
  static const Color warning = Color(0xFFFF9800);      // Your warning color
  static const Color error = Color(0xFFF44336);        // Your error color
}
```

#### **Popular Color Schemes**
```dart
// E-commerce Blue (Amazon-style)
static const Color primary = Color(0xFF232F3E);
static const Color secondary = Color(0xFFFF9900);

// Modern Purple (Shopify-style)
static const Color primary = Color(0xFF7C3AED);
static const Color secondary = Color(0xFF10B981);

// Elegant Black (Apple-style)
static const Color primary = Color(0xFF000000);
static const Color secondary = Color(0xFF007AFF);

// Warm Orange (Etsy-style)
static const Color primary = Color(0xFFD1641C);
static const Color secondary = Color(0xFF00A651);
```

### **3. Typography Customization**

#### **Custom Fonts**
1. Add font files to `assets/fonts/`
2. Update `pubspec.yaml`:
```yaml
flutter:
  fonts:
    - family: YourBrandFont
      fonts:
        - asset: assets/fonts/YourFont-Regular.ttf
        - asset: assets/fonts/YourFont-Bold.ttf
          weight: 700
```

3. Update `lib/core/theme/app_text_styles.dart`:
```dart
class AppTextStyles {
  static const String _fontFamily = 'YourBrandFont';
  
  static const TextStyle titleLarge = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    fontFamily: _fontFamily,
    color: AppColors.textPrimary,
  );
  // ... update other styles
}
```

### **4. App Icon Customization**

#### **Create App Icon**
1. Design 1024x1024px icon
2. Use online generator or create manually:
   - Android: `android/app/src/main/res/mipmap-*/ic_launcher.png`
   - iOS: `ios/Runner/Assets.xcassets/AppIcon.appiconset/`

#### **Using Flutter Launcher Icons**
1. Add dependency:
```yaml
dev_dependencies:
  flutter_launcher_icons: ^0.13.1

flutter_icons:
  android: true
  ios: true
  image_path: "assets/icon/app_icon.png"
```

2. Run: `flutter pub run flutter_launcher_icons:main`

## 🛍️ **Product Customization**

### **1. Product Data**

#### **Add Your Products**
Edit `lib/core/data/mock_data.dart`:
```dart
static List<Product> get products => [
  Product(
    id: 'your_product_1',
    name: 'Your Product Name',
    description: 'Detailed product description with features and benefits',
    price: 99.99,
    originalPrice: 129.99, // For showing discounts
    images: [
      'https://your-cdn.com/product1-image1.jpg',
      'https://your-cdn.com/product1-image2.jpg',
    ],
    category: 'Your Category',
    rating: 4.5,
    reviewCount: 128,
    isInStock: true,
    colors: ['Black', 'White', 'Blue'],
    sizes: ['S', 'M', 'L', 'XL'],
    brand: 'Your Brand',
    isFeatured: true,
    isOnSale: true,
    createdAt: DateTime.now(),
  ),
  // Add more products...
];
```

#### **Product Categories**
```dart
static List<Category> get categories => [
  Category(
    id: 'electronics',
    name: 'Electronics',
    image: 'assets/images/category_electronics.png',
    icon: '📱', // Use relevant emoji
    productCount: 156,
  ),
  Category(
    id: 'fashion',
    name: 'Fashion',
    image: 'assets/images/category_fashion.png',
    icon: '👕',
    productCount: 234,
  ),
  // Add your categories...
];
```

### **2. Pricing Configuration**

#### **Currency Settings**
Create `lib/core/config/app_config.dart`:
```dart
class AppConfig {
  static const String currency = '\$';        // USD
  static const String currencyCode = 'USD';
  static const double taxRate = 0.08;         // 8% tax
  static const double freeShippingThreshold = 100.0;
  static const double shippingCost = 9.99;
}
```

#### **International Currencies**
```dart
// Euro
static const String currency = '€';
static const String currencyCode = 'EUR';

// British Pound
static const String currency = '£';
static const String currencyCode = 'GBP';

// Japanese Yen
static const String currency = '¥';
static const String currencyCode = 'JPY';
```

## 🎨 **UI Customization**

### **1. Home Screen Layout**

#### **Banner Images**
Replace banner images in mock data:
```dart
static List<String> get bannerImages => [
  'https://your-cdn.com/banner1.jpg',
  'https://your-cdn.com/banner2.jpg',
  'https://your-cdn.com/banner3.jpg',
];
```

#### **Section Titles**
Edit `lib/features/home/<USER>/home_screen.dart`:
```dart
// Change section titles
Text(
  'Your Featured Products',  // Instead of 'Featured Products'
  style: AppTextStyles.titleLarge.copyWith(
    fontWeight: FontWeight.bold,
  ),
),
```

### **2. Search Customization**

#### **Trending Searches**
Edit `lib/features/search/presentation/search_screen.dart`:
```dart
List<String> _trendingSearches = [
  'Your Popular Product 1',
  'Your Popular Product 2',
  'Seasonal Collection',
  'Best Sellers',
  'New Arrivals',
  'Sale Items',
];
```

#### **Search Placeholder**
```dart
TextField(
  decoration: InputDecoration(
    hintText: 'Search your store...', // Customize placeholder
    // ...
  ),
),
```

### **3. Profile Screen Customization**

#### **User Stats Labels**
Edit `lib/features/profile/presentation/profile_screen.dart`:
```dart
// Customize stat card titles
_buildStatCard(
  'Purchases',     // Instead of 'Orders'
  _totalOrders.toString(),
  Icons.shopping_bag_outlined,
  AppColors.primary,
),
```

#### **Menu Items**
```dart
// Add custom menu items
_buildMenuItem(
  'Your Custom Feature',
  Icons.your_custom_icon,
  () {
    // Navigate to your custom screen
  },
),
```

## 🔧 **Business Logic Customization**

### **1. Cart Logic**

#### **Shipping Rules**
Edit `lib/features/cart/presentation/cart_screen.dart`:
```dart
double get _shipping {
  if (_subtotal > 75) return 0.0;      // Free shipping over $75
  if (_subtotal > 50) return 4.99;     // Reduced shipping over $50
  return 9.99;                         // Standard shipping
}
```

#### **Tax Calculation**
```dart
double get _tax {
  return _subtotal * 0.10; // 10% tax rate
}
```

### **2. Discount System**

#### **Promo Codes**
```dart
Map<String, double> _promoCodes = {
  'WELCOME10': 0.10,    // 10% discount
  'SAVE20': 0.20,       // 20% discount
  'FREESHIP': 0.0,      // Free shipping
};
```

## 🌐 **Localization Setup**

### **1. Multi-Language Support**

#### **Add Intl Package**
```yaml
dependencies:
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.0
```

#### **Create Language Files**
Create `lib/l10n/app_en.arb`:
```json
{
  "appTitle": "Your Store",
  "searchHint": "Search products...",
  "addToCart": "Add to Cart",
  "checkout": "Checkout"
}
```

Create `lib/l10n/app_es.arb`:
```json
{
  "appTitle": "Tu Tienda",
  "searchHint": "Buscar productos...",
  "addToCart": "Añadir al Carrito",
  "checkout": "Pagar"
}
```

## 📱 **Platform-Specific Customization**

### **1. Android Customization**

#### **App Theme**
Edit `android/app/src/main/res/values/styles.xml`:
```xml
<style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
    <item name="android:colorPrimary">#1976D2</item>
    <item name="android:colorPrimaryDark">#1565C0</item>
    <item name="android:colorAccent">#FF4081</item>
</style>
```

#### **Package Name**
Edit `android/app/build.gradle`:
```gradle
android {
    defaultConfig {
        applicationId "com.agscompany.yourstore"
        // ...
    }
}
```

### **2. iOS Customization**

#### **Bundle Identifier**
Open `ios/Runner.xcworkspace` in Xcode:
1. Select Runner project
2. Change Bundle Identifier to `com.agscompany.yourstore`

#### **App Info**
Edit `ios/Runner/Info.plist`:
```xml
<key>CFBundleName</key>
<string>Your Store</string>
<key>CFBundleDisplayName</key>
<string>Your Store</string>
```

## 🚀 **Advanced Customization**

### **1. Custom Widgets**

#### **Create Custom Product Card**
```dart
class CustomProductCard extends StatelessWidget {
  final Product product;
  
  const CustomProductCard({Key? key, required this.product}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      // Your custom design
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        // Your custom layout
      ),
    );
  }
}
```

### **2. Custom Animations**

#### **Add Page Transitions**
```dart
class CustomPageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  
  CustomPageRoute({required this.child})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(1.0, 0.0), end: Offset.zero),
              ),
              child: child,
            );
          },
        );
}
```

## 📊 **Analytics Integration**

### **1. Firebase Analytics**
```yaml
dependencies:
  firebase_analytics: ^10.1.0
```

```dart
class AnalyticsService {
  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  
  static Future<void> logPurchase(double value, String currency) async {
    await analytics.logPurchase(
      value: value,
      currency: currency,
    );
  }
  
  static Future<void> logSearch(String searchTerm) async {
    await analytics.logSearch(searchTerm: searchTerm);
  }
}
```

## 🔒 **Security Configuration**

### **1. API Keys Management**
Create `lib/core/config/secrets.dart`:
```dart
class Secrets {
  static const String apiKey = String.fromEnvironment('API_KEY');
  static const String stripeKey = String.fromEnvironment('STRIPE_KEY');
}
```

Run with environment variables:
```bash
flutter run --dart-define=API_KEY=your_api_key
```

---

**This customization guide covers all major aspects of personalizing the app for your brand and business needs. Each section includes practical examples and code snippets for easy implementation.**

**© 2025 AGS Company. All rights reserved.**
**Contact: <EMAIL> (licensing inquiries only)**
**Note: No technical support provided. Use Flutter community resources for development assistance.**
