class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final double? originalPrice;
  final List<String> images;
  final String category;
  final double rating;
  final int reviewCount;
  final bool isInStock;
  final List<String> colors;
  final List<String> sizes;
  final String brand;
  final bool isFeatured;
  final bool isOnSale;
  final DateTime createdAt;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.originalPrice,
    required this.images,
    required this.category,
    required this.rating,
    required this.reviewCount,
    required this.isInStock,
    required this.colors,
    required this.sizes,
    required this.brand,
    this.isFeatured = false,
    this.isOnSale = false,
    required this.createdAt,
  });

  double get discountPercentage {
    if (originalPrice == null || originalPrice! <= price) return 0;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }

  bool get hasDiscount => originalPrice != null && originalPrice! > price;

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      price: json['price'].toDouble(),
      originalPrice: json['originalPrice']?.toDouble(),
      images: List<String>.from(json['images']),
      category: json['category'],
      rating: json['rating'].toDouble(),
      reviewCount: json['reviewCount'],
      isInStock: json['isInStock'],
      colors: List<String>.from(json['colors']),
      sizes: List<String>.from(json['sizes']),
      brand: json['brand'],
      isFeatured: json['isFeatured'] ?? false,
      isOnSale: json['isOnSale'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'originalPrice': originalPrice,
      'images': images,
      'category': category,
      'rating': rating,
      'reviewCount': reviewCount,
      'isInStock': isInStock,
      'colors': colors,
      'sizes': sizes,
      'brand': brand,
      'isFeatured': isFeatured,
      'isOnSale': isOnSale,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class Category {
  final String id;
  final String name;
  final String image;
  final String icon;
  final int productCount;

  Category({
    required this.id,
    required this.name,
    required this.image,
    required this.icon,
    required this.productCount,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'],
      name: json['name'],
      image: json['image'],
      icon: json['icon'],
      productCount: json['productCount'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'icon': icon,
      'productCount': productCount,
    };
  }
}
