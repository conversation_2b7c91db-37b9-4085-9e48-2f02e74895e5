import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/models/order.dart';
import '../../../../shared/models/product.dart';
import '../../../../shared/widgets/address_card.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/order_summary_card.dart';

class ShippingStep extends StatefulWidget {
  final CheckoutState checkoutState;
  final Function(ShippingAddress) onAddressSelected;
  final VoidCallback onContinue;

  const ShippingStep({
    super.key,
    required this.checkoutState,
    required this.onAddressSelected,
    required this.onContinue,
  });

  @override
  State<ShippingStep> createState() => _ShippingStepState();
}

class _ShippingStepState extends State<ShippingStep> {
  late List<ShippingAddress> _addresses;
  ShippingAddress? _selectedAddress;
  bool _showAddressForm = false;
  bool _isAddingAddress = false;

  // Form controllers
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressLine1Controller = TextEditingController();
  final _addressLine2Controller = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _postalCodeController = TextEditingController();
  final _countryController = TextEditingController();

  @override
  void initState() {
    super.initState();
    try {
      _addresses = _getMockAddresses();
      _selectedAddress = widget.checkoutState.shippingAddress;
      // Don't auto-select anything - let user choose explicitly
    } catch (e) {
      // Fallback to empty state
      _addresses = [];
      _selectedAddress = null;
    }
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _addressLine1Controller.dispose();
    _addressLine2Controller.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _postalCodeController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Saved Addresses
          if (_addresses.isNotEmpty && !_showAddressForm) ...[
            Text(
              'Saved Addresses',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ..._addresses.map((address) => AddressCard(
              address: address,
              isSelected: _selectedAddress?.id == address.id,
              onTap: () => _selectAddress(address),
              onEdit: () => _editAddress(address),
              onDelete: () => _deleteAddress(address),
            )),
            const SizedBox(height: 16),
            PrimaryButton(
              text: 'Add New Address',
              icon: const Icon(Icons.add, size: 18),
              onPressed: _showNewAddressForm,
            ),
          ],
          
          // Address Form
          if (_showAddressForm) ...[
            Row(
              children: [
                IconButton(
                  onPressed: _hideAddressForm,
                  icon: const Icon(Icons.arrow_back),
                ),
                Text(
                  _isAddingAddress ? 'Add New Address' : 'Edit Address',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildAddressForm(),
          ],
          
          const SizedBox(height: 24),
          
          // Order Summary
          CompactOrderSummary(
            orderSummary: widget.checkoutState.orderSummary,
          ),
          
          const SizedBox(height: 24),
          
          // Continue Button
          PrimaryButton(
            text: 'Continue to Payment',
            width: double.infinity,
            onPressed: _selectedAddress != null ? widget.onContinue : () {
              // Auto-select the first address if none is selected
              if (_addresses.isNotEmpty) {
                final defaultAddress = _addresses.firstWhere(
                  (address) => address.isDefault,
                  orElse: () => _addresses.first,
                );
                _selectAddress(defaultAddress);
                widget.onContinue();
              }
            },
          ),
        ],
      ),
    );
  }

  void _selectAddress(ShippingAddress address) {
    setState(() {
      _selectedAddress = address;
    });
    widget.onAddressSelected(address);
  }

  void _showNewAddressForm() {
    _clearForm();
    setState(() {
      _showAddressForm = true;
      _isAddingAddress = true;
    });
  }

  void _editAddress(ShippingAddress address) {
    _populateForm(address);
    setState(() {
      _showAddressForm = true;
      _isAddingAddress = false;
    });
  }

  void _hideAddressForm() {
    setState(() {
      _showAddressForm = false;
    });
  }

  void _clearForm() {
    _fullNameController.clear();
    _phoneController.clear();
    _addressLine1Controller.clear();
    _addressLine2Controller.clear();
    _cityController.clear();
    _stateController.clear();
    _postalCodeController.clear();
    _countryController.clear();
  }

  void _populateForm(ShippingAddress address) {
    _fullNameController.text = address.fullName;
    _phoneController.text = address.phoneNumber;
    _addressLine1Controller.text = address.addressLine1;
    _addressLine2Controller.text = address.addressLine2 ?? '';
    _cityController.text = address.city;
    _stateController.text = address.state;
    _postalCodeController.text = address.postalCode;
    _countryController.text = address.country;
  }

  void _saveAddress() {
    if (_formKey.currentState?.validate() != true) return;

    final newAddress = ShippingAddress(
      id: _isAddingAddress ? DateTime.now().toString() : _selectedAddress!.id,
      fullName: _fullNameController.text,
      phoneNumber: _phoneController.text,
      addressLine1: _addressLine1Controller.text,
      addressLine2: _addressLine2Controller.text.isEmpty 
          ? null 
          : _addressLine2Controller.text,
      city: _cityController.text,
      state: _stateController.text,
      postalCode: _postalCodeController.text,
      country: _countryController.text,
    );

    setState(() {
      if (_isAddingAddress) {
        _addresses.add(newAddress);
      } else {
        final index = _addresses.indexWhere((a) => a.id == newAddress.id);
        if (index >= 0) {
          _addresses[index] = newAddress;
        }
      }
      _selectedAddress = newAddress;
      _showAddressForm = false;
    });
    
    widget.onAddressSelected(newAddress);
  }

  void _deleteAddress(ShippingAddress address) {
    setState(() {
      _addresses.removeWhere((a) => a.id == address.id);
      if (_selectedAddress?.id == address.id) {
        _selectedAddress = _addresses.isNotEmpty ? _addresses.first : null;
        if (_selectedAddress != null) {
          widget.onAddressSelected(_selectedAddress!);
        }
      }
    });
  }

  Widget _buildAddressForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          CustomTextField(
            controller: _fullNameController,
            label: 'Full Name',
            hintText: 'Enter your full name',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your full name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          PhoneTextField(
            controller: _phoneController,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your phone number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _addressLine1Controller,
            label: 'Address Line 1',
            hintText: 'Street address, P.O. box',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your address';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _addressLine2Controller,
            label: 'Address Line 2 (Optional)',
            hintText: 'Apartment, suite, unit, building, floor, etc.',
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: _cityController,
                  label: 'City',
                  hintText: 'Enter city',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Required';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomTextField(
                  controller: _stateController,
                  label: 'State/Province',
                  hintText: 'Enter state',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Required';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: _postalCodeController,
                  label: 'Postal Code',
                  hintText: 'Enter postal code',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Required';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomTextField(
                  controller: _countryController,
                  label: 'Country',
                  hintText: 'Enter country',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Required';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          PrimaryButton(
            text: 'Save Address',
            width: double.infinity,
            onPressed: _saveAddress,
          ),
        ],
      ),
    );
  }

  // Mock data for demonstration
  List<ShippingAddress> _getMockAddresses() {
    return [
      ShippingAddress(
        id: '1',
        fullName: 'John Doe',
        phoneNumber: '+****************',
        addressLine1: '123 Main Street',
        city: 'New York',
        state: 'NY',
        postalCode: '10001',
        country: 'United States',
        isDefault: true,
      ),
      ShippingAddress(
        id: '2',
        fullName: 'John Doe',
        phoneNumber: '+****************',
        addressLine1: '456 Park Avenue',
        addressLine2: 'Apt 7B',
        city: 'Los Angeles',
        state: 'CA',
        postalCode: '90001',
        country: 'United States',
      ),
    ];
  }
}
