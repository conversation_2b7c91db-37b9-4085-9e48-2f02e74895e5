import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';

class CheckoutStepIndicator extends StatelessWidget {
  final int currentStep;
  final List<String> steps;
  final Color activeColor;
  final Color inactiveColor;
  final Color completedColor;

  const CheckoutStepIndicator({
    super.key,
    required this.currentStep,
    required this.steps,
    this.activeColor = AppColors.primary,
    this.inactiveColor = AppColors.greyLight,
    this.completedColor = AppColors.success,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: List.generate(
          steps.length,
          (index) => Expanded(
            child: Row(
              children: [
                Expanded(
                  child: _buildStep(index),
                ),
                if (index < steps.length - 1)
                  Expanded(
                    child: _buildConnector(index),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStep(int index) {
    final isActive = index == currentStep;
    final isCompleted = index < currentStep;
    final isInactive = index > currentStep;

    Color stepColor;
    Color textColor;
    Widget stepContent;

    if (isCompleted) {
      stepColor = completedColor;
      textColor = AppColors.white;
      stepContent = const Icon(
        Icons.check,
        color: AppColors.white,
        size: 16,
      );
    } else if (isActive) {
      stepColor = activeColor;
      textColor = AppColors.white;
      stepContent = Text(
        '${index + 1}',
        style: AppTextStyles.labelSmall.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      );
    } else {
      stepColor = inactiveColor;
      textColor = AppColors.textSecondary;
      stepContent = Text(
        '${index + 1}',
        style: AppTextStyles.labelSmall.copyWith(
          color: textColor,
        ),
      );
    }

    return Column(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: stepColor,
            shape: BoxShape.circle,
            border: isInactive
                ? Border.all(color: AppColors.border, width: 2)
                : null,
          ),
          child: Center(child: stepContent),
        ),
        const SizedBox(height: 8),
        Text(
          steps[index],
          style: AppTextStyles.bodySmall.copyWith(
            color: isActive ? activeColor : textColor,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildConnector(int index) {
    final isCompleted = index < currentStep;
    final isActive = index == currentStep - 1;

    return Container(
      height: 2,
      margin: const EdgeInsets.only(bottom: 40),
      decoration: BoxDecoration(
        color: isCompleted || isActive ? activeColor : inactiveColor,
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }
}

// Animated version with smooth transitions
class AnimatedCheckoutStepIndicator extends StatefulWidget {
  final int currentStep;
  final List<String> steps;
  final Color activeColor;
  final Color inactiveColor;
  final Color completedColor;
  final Duration animationDuration;

  const AnimatedCheckoutStepIndicator({
    super.key,
    required this.currentStep,
    required this.steps,
    this.activeColor = AppColors.primary,
    this.inactiveColor = AppColors.greyLight,
    this.completedColor = AppColors.success,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<AnimatedCheckoutStepIndicator> createState() =>
      _AnimatedCheckoutStepIndicatorState();
}

class _AnimatedCheckoutStepIndicatorState
    extends State<AnimatedCheckoutStepIndicator>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Color?>> _colorAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.steps.length,
      (index) => AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      ),
    );

    _scaleAnimations = _controllers
        .map((controller) => Tween<double>(
              begin: 0.8,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: controller,
              curve: Curves.elasticOut,
            )))
        .toList();

    _colorAnimations = _controllers
        .map((controller) => ColorTween(
              begin: widget.inactiveColor,
              end: widget.activeColor,
            ).animate(controller))
        .toList();

    // Start animations for completed and current steps
    for (int i = 0; i <= widget.currentStep && i < _controllers.length; i++) {
      _controllers[i].forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedCheckoutStepIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentStep != widget.currentStep) {
      _updateAnimations();
    }
  }

  void _updateAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      if (i <= widget.currentStep) {
        _controllers[i].forward();
      } else {
        _controllers[i].reverse();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: List.generate(
          widget.steps.length,
          (index) => Expanded(
            child: Row(
              children: [
                Expanded(
                  child: _buildAnimatedStep(index),
                ),
                if (index < widget.steps.length - 1)
                  Expanded(
                    child: _buildAnimatedConnector(index),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedStep(int index) {
    final isActive = index == widget.currentStep;
    final isCompleted = index < widget.currentStep;

    return AnimatedBuilder(
      animation: _controllers[index],
      builder: (context, child) {
        return Column(
          children: [
            ScaleTransition(
              scale: _scaleAnimations[index],
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: isCompleted
                      ? widget.completedColor
                      : _colorAnimations[index].value,
                  shape: BoxShape.circle,
                  boxShadow: isActive
                      ? [
                          BoxShadow(
                            color: widget.activeColor.withOpacity(0.3),
                            blurRadius: 8,
                            spreadRadius: 2,
                          ),
                        ]
                      : null,
                ),
                child: Center(
                  child: isCompleted
                      ? const Icon(
                          Icons.check,
                          color: AppColors.white,
                          size: 16,
                        )
                      : Text(
                          '${index + 1}',
                          style: AppTextStyles.labelSmall.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            AnimatedDefaultTextStyle(
              duration: widget.animationDuration,
              style: AppTextStyles.bodySmall.copyWith(
                color: isActive ? widget.activeColor : AppColors.textSecondary,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
              ),
              child: Text(
                widget.steps[index],
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAnimatedConnector(int index) {
    final isCompleted = index < widget.currentStep;
    final isActive = index == widget.currentStep - 1;

    return AnimatedContainer(
      duration: widget.animationDuration,
      height: 2,
      margin: const EdgeInsets.only(bottom: 40),
      decoration: BoxDecoration(
        color: isCompleted || isActive
            ? widget.activeColor
            : widget.inactiveColor,
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }
}
