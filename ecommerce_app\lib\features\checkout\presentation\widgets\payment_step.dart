import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../shared/models/order.dart';
import '../../../../shared/models/product.dart';
import '../../../../shared/widgets/payment_method_card.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/order_summary_card.dart';

class PaymentStep extends StatefulWidget {
  final CheckoutState checkoutState;
  final Function(PaymentMethod) onPaymentMethodSelected;
  final VoidCallback onContinue;
  final VoidCallback onBack;

  const PaymentStep({
    super.key,
    required this.checkoutState,
    required this.onPaymentMethodSelected,
    required this.onContinue,
    required this.onBack,
  });

  @override
  State<PaymentStep> createState() => _PaymentStepState();
}

class _PaymentStepState extends State<PaymentStep> {
  late List<PaymentMethod> _paymentMethods;
  PaymentMethod? _selectedPaymentMethod;
  bool _showPaymentForm = false;
  bool _isAddingPayment = false;

  // Form controllers
  final _formKey = GlobalKey<FormState>();
  final _cardNumberController = TextEditingController();
  final _expiryController = TextEditingController();
  final _cvvController = TextEditingController();
  final _cardHolderController = TextEditingController();

  @override
  void initState() {
    super.initState();
    try {
      _paymentMethods = _getMockPaymentMethods();
      _selectedPaymentMethod = widget.checkoutState.paymentMethod;
      // Don't auto-select anything - let user choose explicitly
    } catch (e) {
      // Fallback to empty state
      _paymentMethods = [];
      _selectedPaymentMethod = null;
    }
  }

  @override
  void dispose() {
    _cardNumberController.dispose();
    _expiryController.dispose();
    _cvvController.dispose();
    _cardHolderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Saved Payment Methods
          if (_paymentMethods.isNotEmpty && !_showPaymentForm) ...[
            Text(
              'Payment Methods',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ..._paymentMethods.map((method) => PaymentMethodCard(
              paymentMethod: method,
              isSelected: _selectedPaymentMethod?.id == method.id,
              onTap: () => _selectPaymentMethod(method),
              onEdit: () => _editPaymentMethod(method),
              onDelete: () => _deletePaymentMethod(method),
            )),
            const SizedBox(height: 16),
            
            // Quick Payment Options
            _buildQuickPaymentOptions(),
            
            const SizedBox(height: 16),
            PrimaryButton(
              text: 'Add New Card',
              icon: const Icon(Icons.add, size: 18),
              onPressed: _showNewPaymentForm,
            ),
          ],
          
          // Payment Form
          if (_showPaymentForm) ...[
            Row(
              children: [
                IconButton(
                  onPressed: _hidePaymentForm,
                  icon: const Icon(Icons.arrow_back),
                ),
                Text(
                  _isAddingPayment ? 'Add New Card' : 'Edit Card',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPaymentForm(),
          ],
          
          const SizedBox(height: 24),
          
          // Order Summary
          CompactOrderSummary(
            orderSummary: widget.checkoutState.orderSummary,
          ),
          
          const SizedBox(height: 24),
          
          // Navigation Buttons
          Row(
            children: [
              Expanded(
                child: CustomOutlineButton(
                  text: 'Back',
                  onPressed: widget.onBack,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: PrimaryButton(
                  text: 'Review Order',
                  onPressed: _selectedPaymentMethod != null ? widget.onContinue : () {
                    // Auto-select the first payment method if none is selected
                    if (_paymentMethods.isNotEmpty) {
                      final defaultMethod = _paymentMethods.firstWhere(
                        (method) => method.isDefault,
                        orElse: () => _paymentMethods.first,
                      );
                      _selectPaymentMethod(defaultMethod);
                      widget.onContinue();
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickPaymentOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Payment',
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickPaymentButton(
                'Apple Pay',
                Icons.phone_iphone,
                AppColors.black,
                () => _selectQuickPayment(PaymentMethodType.applePay),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickPaymentButton(
                'Google Pay',
                Icons.android,
                AppColors.success,
                () => _selectQuickPayment(PaymentMethodType.googlePay),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickPaymentButton(
                'PayPal',
                Icons.account_balance_wallet,
                AppColors.info,
                () => _selectQuickPayment(PaymentMethodType.paypal),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickPaymentButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.border),
          ),
          child: Column(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(height: 8),
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectPaymentMethod(PaymentMethod method) {
    setState(() {
      _selectedPaymentMethod = method;
    });
    widget.onPaymentMethodSelected(method);
  }

  void _selectQuickPayment(PaymentMethodType type) {
    final quickPayment = PaymentMethod(
      id: 'quick_${type.toString()}',
      type: type,
      displayName: type.toString().split('.').last,
    );
    
    setState(() {
      _selectedPaymentMethod = quickPayment;
    });
    widget.onPaymentMethodSelected(quickPayment);
  }

  void _showNewPaymentForm() {
    _clearForm();
    setState(() {
      _showPaymentForm = true;
      _isAddingPayment = true;
    });
  }

  void _editPaymentMethod(PaymentMethod method) {
    _populateForm(method);
    setState(() {
      _showPaymentForm = true;
      _isAddingPayment = false;
    });
  }

  void _hidePaymentForm() {
    setState(() {
      _showPaymentForm = false;
    });
  }

  void _clearForm() {
    _cardNumberController.clear();
    _expiryController.clear();
    _cvvController.clear();
    _cardHolderController.clear();
  }

  void _populateForm(PaymentMethod method) {
    _cardHolderController.text = method.cardHolderName ?? '';
    _expiryController.text = method.expiryDate ?? '';
    // Note: We don't populate card number and CVV for security reasons
  }

  void _savePaymentMethod() {
    if (_formKey.currentState?.validate() != true) return;

    final newMethod = PaymentMethod(
      id: _isAddingPayment ? DateTime.now().toString() : _selectedPaymentMethod!.id,
      type: PaymentMethodType.creditCard,
      displayName: 'Credit Card',
      last4Digits: _cardNumberController.text.replaceAll(' ', '').substring(
        _cardNumberController.text.replaceAll(' ', '').length - 4,
      ),
      expiryDate: _expiryController.text,
      cardHolderName: _cardHolderController.text,
    );

    setState(() {
      if (_isAddingPayment) {
        _paymentMethods.add(newMethod);
      } else {
        final index = _paymentMethods.indexWhere((m) => m.id == newMethod.id);
        if (index >= 0) {
          _paymentMethods[index] = newMethod;
        }
      }
      _selectedPaymentMethod = newMethod;
      _showPaymentForm = false;
    });
    
    widget.onPaymentMethodSelected(newMethod);
  }

  void _deletePaymentMethod(PaymentMethod method) {
    setState(() {
      _paymentMethods.removeWhere((m) => m.id == method.id);
      if (_selectedPaymentMethod?.id == method.id) {
        _selectedPaymentMethod = _paymentMethods.isNotEmpty ? _paymentMethods.first : null;
        if (_selectedPaymentMethod != null) {
          widget.onPaymentMethodSelected(_selectedPaymentMethod!);
        }
      }
    });
  }

  Widget _buildPaymentForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          CustomTextField(
            controller: _cardHolderController,
            label: 'Card Holder Name',
            hintText: 'Enter name on card',
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter card holder name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _cardNumberController,
            label: 'Card Number',
            hintText: '1234 5678 9012 3456',
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter card number';
              }
              if (value.replaceAll(' ', '').length < 16) {
                return 'Please enter a valid card number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: _expiryController,
                  label: 'Expiry Date',
                  hintText: 'MM/YY',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Required';
                    }
                    if (!RegExp(r'^\d{2}/\d{2}$').hasMatch(value)) {
                      return 'Invalid format';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomTextField(
                  controller: _cvvController,
                  label: 'CVV',
                  hintText: '123',
                  keyboardType: TextInputType.number,
                  obscureText: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Required';
                    }
                    if (value.length < 3) {
                      return 'Invalid CVV';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          PrimaryButton(
            text: 'Save Card',
            width: double.infinity,
            onPressed: _savePaymentMethod,
          ),
        ],
      ),
    );
  }

  // Mock data for demonstration
  List<PaymentMethod> _getMockPaymentMethods() {
    return [
      PaymentMethod(
        id: '1',
        type: PaymentMethodType.creditCard,
        displayName: 'Visa Credit Card',
        last4Digits: '4567',
        expiryDate: '12/25',
        cardHolderName: 'John Doe',
        isDefault: true,
      ),
      PaymentMethod(
        id: '2',
        type: PaymentMethodType.debitCard,
        displayName: 'Mastercard Debit',
        last4Digits: '8901',
        expiryDate: '08/26',
        cardHolderName: 'John Doe',
      ),
    ];
  }
}
