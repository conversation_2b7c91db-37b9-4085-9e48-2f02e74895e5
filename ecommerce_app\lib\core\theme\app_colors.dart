import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - Vibrant Purple Theme
  static const Color primary = Color(0xFF6C5CE7);        // Beautiful Purple
  static const Color primaryDark = Color(0xFF5A4FCF);    // Dark Purple
  static const Color primaryLight = Color(0xFFDDD6FE);   // Light Purple

  // Secondary Colors - Turquoise Accent
  static const Color secondary = Color(0xFF00D2D3);      // Turquoise
  static const Color secondaryDark = Color(0xFF00A8A9);  // Dark Turquoise
  static const Color secondaryLight = Color(0xFFB3F5F6); // Light Turquoise

  // Accent Colors - Coral Pink
  static const Color accent = Color(0xFFFF6B6B);         // Coral Pink
  static const Color accentDark = Color(0xFFE55555);     // Dark Coral
  static const Color accentLight = Color(0xFFFFE0E0);    // Light Coral
  
  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF9E9E9E);
  static const Color greyLight = Color(0xFFF5F5F5);
  static const Color greyDark = Color(0xFF424242);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textWhite = Color(0xFFFFFFFF);
  
  // Background Colors - Soft and Modern
  static const Color background = Color(0xFFF8F9FF);     // Very Light Purple Tint
  static const Color surface = Color(0xFFFFFFFF);        // Pure White
  static const Color card = Color(0xFFFFFFFF);           // Pure White
  
  // Status Colors - Vibrant and Clear
  static const Color success = Color(0xFF00B894);        // Mint Green
  static const Color warning = Color(0xFFE17055);        // Orange
  static const Color error = Color(0xFFE84393);          // Pink Red
  static const Color info = Color(0xFF74B9FF);           // Sky Blue
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderLight = Color(0xFFF0F0F0);
  static const Color borderDark = Color(0xFFBDBDBD);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x33000000);
  
  // Gradient Colors - Beautiful Multi-Color Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, accentDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Rainbow Gradient for Special Elements
  static const LinearGradient rainbowGradient = LinearGradient(
    colors: [primary, secondary, accent],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Sunset Gradient for Banners
  static const LinearGradient sunsetGradient = LinearGradient(
    colors: [Color(0xFFFF6B6B), Color(0xFFFFE66D), Color(0xFF4ECDC4)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Rating Colors
  static const Color ratingFilled = Color(0xFFFFD700);
  static const Color ratingEmpty = Color(0xFFE0E0E0);
  
  // Price Colors
  static const Color priceOriginal = Color(0xFF757575);
  static const Color priceDiscounted = Color(0xFFF44336);
  static const Color priceSale = Color(0xFF4CAF50);
  
  // Category Colors - Vibrant and Distinct
  static const List<Color> categoryColors = [
    Color(0xFF74B9FF),  // Electronics - Sky Blue
    Color(0xFFE84393),  // Fashion - Pink
    Color(0xFF00B894),  // Home - Mint Green
    Color(0xFFE17055),  // Beauty - Orange
    Color(0xFF6C5CE7),  // Sports - Purple
    Color(0xFFFFD93D),  // Books - Yellow
    Color(0xFF00D2D3),  // Toys - Turquoise
    Color(0xFFFF6B6B),  // Food - Coral
  ];

  // Individual Category Colors for Easy Access
  static const Color categoryElectronics = Color(0xFF74B9FF);
  static const Color categoryFashion = Color(0xFFE84393);
  static const Color categoryHome = Color(0xFF00B894);
  static const Color categoryBeauty = Color(0xFFE17055);
  static const Color categorySports = Color(0xFF6C5CE7);
  static const Color categoryBooks = Color(0xFFFFD93D);
  static const Color categoryToys = Color(0xFF00D2D3);
  static const Color categoryFood = Color(0xFFFF6B6B);
}
